# WordViewer Component

Component tái sử dụng để hiển thị file Word (.docx) trong modal với 2 phương thức xem: Microsoft Office Online và Mammoth.js HTML conversion.

## 🚀 Cách sử dụng cơ bản

### 1. Import component
```typescript
import { WordViewer } from "@src/components/WordViewer";
```

### 2. Sử dụng với state management thủ công
```typescript
const [isWordOpen, setIsWordOpen] = useState(false);
const [wordUrl, setWordUrl] = useState("");

<WordViewer
  fileUrl={wordUrl}
  fileName="Document.docx"
  open={isWordOpen}
  onClose={() => setIsWordOpen(false)}
  viewerMode="both" // 'office' | 'mammoth' | 'both'
/>
```

### 3. Sử dụng với custom hook (khuyến nghị)
```typescript
import { WordViewer, useWordViewer } from "@src/components/WordViewer";

const MyComponent = () => {
  const { isOpen, openWordViewer, closeWordViewer, currentFileUrl, currentFileName } = useWordViewer();

  const handleViewWord = () => {
    openWordViewer("https://example.com/document.docx", "My Document", 'both');
  };

  return (
    <>
      <Button onClick={handleViewWord}>Xem Word</Button>
      
      <WordViewer
        fileUrl={currentFileUrl || ""}
        fileName={currentFileName || ""}
        open={isOpen}
        onClose={closeWordViewer}
      />
    </>
  );
};
```

## 📋 Props API

| Prop | Type | Default | Required | Description |
|------|------|---------|----------|-------------|
| `fileUrl` | `string` | - | ✅ | URL của file Word |
| `fileName` | `string` | `"Word Document"` | ❌ | Tên file hiển thị |
| `open` | `boolean` | - | ✅ | Trạng thái mở/đóng |
| `onClose` | `() => void` | - | ✅ | Callback đóng modal |
| `width` | `string \| number` | `"95vw"` | ❌ | Width modal |
| `height` | `string \| number` | `"85vh"` | ❌ | Height modal |
| `destroyOnClose` | `boolean` | `true` | ❌ | Destroy khi đóng |
| `containerStyle` | `CSSProperties` | `{}` | ❌ | Custom style |
| `viewerMode` | `'office' \| 'mammoth' \| 'both'` | `'both'` | ❌ | Chế độ xem |

## 🎨 Viewer Modes

### 1. Office Online (`viewerMode="office"`)
- Sử dụng Microsoft Office Online Viewer
- Hiển thị file Word trong iframe
- **Ưu điểm:** Giữ nguyên format, hỗ trợ đầy đủ tính năng Word
- **Nhược điểm:** File phải public và accessible từ internet

### 2. Mammoth HTML (`viewerMode="mammoth"`)
- Convert DOCX thành HTML sử dụng mammoth.js
- Hiển thị HTML trong div
- **Ưu điểm:** Hoạt động với file private, load nhanh
- **Nhược điểm:** Có thể mất một số formatting phức tạp

### 3. Both (`viewerMode="both"`)
- Hiển thị cả 2 options trong tabs
- User có thể chọn phương thức xem phù hợp
- **Khuyến nghị** cho hầu hết use cases

## 🎨 Preset Components

### WordViewerSmall - Modal nhỏ (70vw x 60vh)
```typescript
import { WordViewerSmall } from "@src/components/WordViewer/WordViewerPresets";

<WordViewerSmall
  fileUrl={wordUrl}
  fileName="Document.docx"
  open={isOpen}
  onClose={onClose}
/>
```

### WordViewerMedium - Modal trung bình (85vw x 75vh)
```typescript
import { WordViewerMedium } from "@src/components/WordViewer/WordViewerPresets";
```

### WordViewerFullscreen - Full screen (98vw x 90vh)
```typescript
import { WordViewerFullscreen } from "@src/components/WordViewer/WordViewerPresets";
```

### WordViewerOfficeOnly - Chỉ Office Online
```typescript
import { WordViewerOfficeOnly } from "@src/components/WordViewer/WordViewerPresets";
```

### WordViewerMammothOnly - Chỉ Mammoth HTML
```typescript
import { WordViewerMammothOnly } from "@src/components/WordViewer/WordViewerPresets";
```

### WordViewerMobile - Tối ưu cho mobile
```typescript
import { WordViewerMobile } from "@src/components/WordViewer/WordViewerPresets";
```

## 🔧 Custom Hook API

### useWordViewer(defaultViewerMode?)

**Parameters:**
- `defaultViewerMode?: 'office' | 'mammoth' | 'both'` - Viewer mode mặc định

**Returns:**
```typescript
{
  isOpen: boolean;
  openWordViewer: (fileUrl: string, fileName?: string, viewerMode?: 'office' | 'mammoth' | 'both') => void;
  closeWordViewer: () => void;
  currentFileUrl: string | null;
  currentFileName: string | null;
  currentViewerMode: 'office' | 'mammoth' | 'both';
}
```

## 💡 Ví dụ nâng cao

### Custom styling
```typescript
<WordViewer
  fileUrl={wordUrl}
  fileName="Custom Document"
  open={isOpen}
  onClose={onClose}
  width="80vw"
  height="70vh"
  containerStyle={{
    border: "2px solid #1890ff",
    borderRadius: "12px",
    boxShadow: "0 4px 12px rgba(0,0,0,0.15)"
  }}
/>
```

### Chỉ sử dụng Office Online
```typescript
<WordViewer
  fileUrl={wordUrl}
  fileName="Office Document"
  open={isOpen}
  onClose={onClose}
  viewerMode="office"
/>
```

### Chỉ sử dụng Mammoth HTML
```typescript
<WordViewer
  fileUrl={wordUrl}
  fileName="HTML Preview Document"
  open={isOpen}
  onClose={onClose}
  viewerMode="mammoth"
/>
```

## 🧪 Test component

### Sử dụng WordViewerDemo
```typescript
import WordViewerDemo from "@src/components/WordViewer/WordViewerDemo";

// Trong App hoặc page component
<WordViewerDemo />
```

## 📦 Dependencies

- **mammoth**: Convert DOCX to HTML
- **antd**: UI components (Modal, Tabs, Button, etc.)
- **react**: Core React hooks

## ⚠️ Lưu ý quan trọng

### Office Online Viewer
- File phải được host public và accessible từ internet
- Không hoạt động với file localhost hoặc private URLs
- Tốt nhất cho file đã upload lên server production

### Mammoth.js
- Chỉ hỗ trợ file .docx (không hỗ trợ .doc)
- Có thể mất một số formatting phức tạp
- Hoạt động tốt với file private và localhost

## 🎯 Khi nào sử dụng gì?

| Tình huống | Khuyến nghị |
|------------|-------------|
| File public trên internet | `viewerMode="office"` |
| File private/localhost | `viewerMode="mammoth"` |
| Không chắc chắn | `viewerMode="both"` |
| Mobile app | `WordViewerMobile` |
| Cần format chính xác 100% | `viewerMode="office"` |
| Cần load nhanh | `viewerMode="mammoth"` |

## 🚀 Sẵn sàng sử dụng

WordViewer component đã hoàn thành và tích hợp vào ModalQuanLyFileCaNhan!

✅ **Đã tích hợp vào hệ thống quản lý file**
✅ **Hỗ trợ 2 phương thức xem**
✅ **Nhiều preset sizes**
✅ **Custom hook tiện lợi**
✅ **Full TypeScript support**
